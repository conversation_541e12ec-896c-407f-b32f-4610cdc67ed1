// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Debt & Inventory App';

  @override
  String get appName => 'Debt & Inventory App';

  @override
  String get home => 'Home';

  @override
  String get homeDashboardTitle => 'Debt & Inventory Dashboard';

  @override
  String get totalDebt => 'Total Debt';

  @override
  String get customers => 'Customers';

  @override
  String get products => 'Products';

  @override
  String get newCustomer => 'New Customer';

  @override
  String get newTransaction => 'New Transaction';

  @override
  String get generateReport => 'Generate Report';

  @override
  String get debtDistribution => 'Debt Distribution';

  @override
  String get pieChartPlaceholder => 'Pie Chart will be displayed here.';

  @override
  String get customersScreenTitle => 'Customers';

  @override
  String get searchCustomers => 'Search Customers';

  @override
  String get addCustomer => 'Add Customer';

  @override
  String get customerName => 'Customer Name';

  @override
  String get phoneNumberOptional => 'Phone Number (Optional)';

  @override
  String get customerAddedSuccessfully => 'Customer added successfully!';

  @override
  String failedToAddCustomer(Object error) {
    return 'Failed to add customer: $error';
  }

  @override
  String get pleaseEnterCustomerName => 'Please enter a customer name';

  @override
  String get customerDetails => 'Customer Details';

  @override
  String get name => 'Name';

  @override
  String get phone => 'Phone';

  @override
  String totalDebtAmount(Object amount) {
    return 'Total Debt: $amount';
  }

  @override
  String get transactionHistory => 'Transaction History';

  @override
  String get addTransaction => 'Add New Transaction';

  @override
  String get customer => 'Customer';

  @override
  String get transactionType => 'Transaction Type';

  @override
  String get amount => 'Amount';

  @override
  String get notesOptional => 'Notes (Optional)';

  @override
  String get transactionAddedSuccessfully => 'Transaction added successfully!';

  @override
  String failedToAddTransaction(Object error) {
    return 'Failed to add transaction: $error';
  }

  @override
  String get pleaseSelectCustomer => 'Please select a customer.';

  @override
  String get pleaseSelectTransactionType => 'Please select a transaction type.';

  @override
  String get pleaseEnterAmount => 'Please enter an amount';

  @override
  String get pleaseEnterValidNumber => 'Please enter a valid number';

  @override
  String get amountGreaterThanZero => 'Amount must be greater than zero';

  @override
  String get debt => 'Debt';

  @override
  String get payment => 'Payment';

  @override
  String get transactions => 'Transactions';

  @override
  String get inventoryManagement => 'Inventory Management';

  @override
  String get searchProducts => 'Search Products';

  @override
  String get addProduct => 'Add Product';

  @override
  String get newProduct => 'New Product';

  @override
  String get productName => 'Product Name';

  @override
  String get purchasePrice => 'Purchase Price';

  @override
  String get stockQuantity => 'Stock Quantity';

  @override
  String get imageURLOptional => 'Image URL (Optional)';

  @override
  String get productAddedSuccessfully => 'Product added successfully!';

  @override
  String failedToAddProduct(Object error) {
    return 'Failed to add product: $error';
  }

  @override
  String get pleaseEnterProductName => 'Please enter a product name';

  @override
  String get pleaseEnterPurchasePrice => 'Please enter a purchase price';

  @override
  String get stockCannotBeNegative => 'Stock cannot be negative';

  @override
  String get pleaseEnterStockQuantity => 'Please enter a stock quantity';

  @override
  String get pleaseEnterValidInteger => 'Please enter a valid integer';

  @override
  String get noPhone => 'No phone';

  @override
  String get n_a => 'N/A';

  @override
  String get transaction => 'Transaction';

  @override
  String get type => 'Type';

  @override
  String get total => 'Total';

  @override
  String get rial => 'Rial';

  @override
  String get date => 'Date';

  @override
  String get description => 'Description';

  @override
  String get remaining => 'Remaining';

  @override
  String get customerNotFound => 'Customer not found';

  @override
  String get exitAppTitle => 'Exit App?';

  @override
  String get exitAppMessage => 'Do you want to exit the application?';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';
}

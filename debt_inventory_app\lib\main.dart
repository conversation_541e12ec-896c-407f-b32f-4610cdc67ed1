import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:debt_inventory_app/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/screens/home_screen.dart';
import 'package:debt_inventory_app/screens/customers_screen.dart';
import 'package:debt_inventory_app/screens/customer_detail_screen.dart';
import 'package:debt_inventory_app/screens/transaction_form_screen.dart';
import 'package:debt_inventory_app/screens/inventory_screen.dart';
import 'package:debt_inventory_app/screens/add_customer_screen.dart';
import 'package:debt_inventory_app/screens/add_product_screen.dart';
import 'package:debt_inventory_app/screens/transactions_screen.dart';
import 'package:debt_inventory_app/providers/locale_provider.dart'; // Moved to top

final databaseProvider = Provider((ref) => AppDatabase());


final GoRouter _router = GoRouter(
  routes: <RouteBase>[
    GoRoute(
      path: '/',
      builder: (BuildContext context, GoRouterState state) {
        return const HomeScreen();
      },
    ),
    GoRoute(
      path: '/customers',
      builder: (BuildContext context, GoRouterState state) {
        return const CustomersScreen();
      },
      routes: <RouteBase>[
        GoRoute(
          path: ':customerId',
          builder: (BuildContext context, GoRouterState state) {
            final customerId = int.parse(state.pathParameters['customerId']!);
            return CustomerDetailScreen(customerId: customerId);
          },
        ),
        GoRoute(
          path: 'new',
          builder: (BuildContext context, GoRouterState state) {
            return const AddCustomerScreen();
          },
        ),
      ],
    ),
    GoRoute(
      path: '/transactions',
      builder: (BuildContext context, GoRouterState state) {
        return const TransactionsScreen();
      },
      routes: <RouteBase>[
        GoRoute(
          path: 'new',
          builder: (BuildContext context, GoRouterState state) {
            final customerId = state.uri.queryParameters['customerId'];
            return TransactionFormScreen(
                customerId: customerId != null ? int.parse(customerId) : null);
          },
        ),
      ],
    ),
    GoRoute(
      path: '/inventory',
      builder: (BuildContext context, GoRouterState state) {
        return const InventoryScreen();
      },
    ),
    GoRoute(
      path: '/inventory/new',
      builder: (BuildContext context, GoRouterState state) {
        return const AddProductScreen();
      },
    ),
  ],
);

void main() {
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  final lightColorScheme = const ColorScheme(
    primary: Color(0xFF006874),
    secondary: Color(0xFF4A6267),
    surface: Color(0xFFFAFDFD),
    onPrimary: Colors.white,
    error: Color(0xFFBA1A1A),
    onSecondary: Colors.white,
    onSurface: Color(0xFF191C1D),
    onError: Colors.white,
    brightness: Brightness.light,
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = ref.watch(localeProvider);
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: 'Debt & Inventory App',
      theme: ThemeData(
        colorScheme: lightColorScheme,
        useMaterial3: true,
      ),
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      locale: locale,
      routerConfig: _router,
    );
  }
}

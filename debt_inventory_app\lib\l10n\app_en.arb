{"@@locale": "en", "appTitle": "Debt & Inventory App", "appName": "Debt & Inventory App", "home": "Home", "homeDashboardTitle": "Debt & Inventory Dashboard", "totalDebt": "Total Debt", "customers": "Customers", "products": "Products", "newCustomer": "New Customer", "newTransaction": "New Transaction", "generateReport": "Generate Report", "debtDistribution": "Debt Distribution", "pieChartPlaceholder": "Pie Chart will be displayed here.", "customersScreenTitle": "Customers", "searchCustomers": "Search Customers", "addCustomer": "Add Customer", "customerName": "Customer Name", "phoneNumberOptional": "Phone Number (Optional)", "customerAddedSuccessfully": "Customer added successfully!", "failedToAddCustomer": "Failed to add customer: {error}", "pleaseEnterCustomerName": "Please enter a customer name", "customerDetails": "Customer Details", "name": "Name", "phone": "Phone", "totalDebtAmount": "Total Debt: {amount}", "transactionHistory": "Transaction History", "addTransaction": "Add New Transaction", "customer": "Customer", "transactionType": "Transaction Type", "amount": "Amount", "notesOptional": "Notes (Optional)", "transactionAddedSuccessfully": "Transaction added successfully!", "failedToAddTransaction": "Failed to add transaction: {error}", "pleaseSelectCustomer": "Please select a customer.", "pleaseSelectTransactionType": "Please select a transaction type.", "pleaseEnterAmount": "Please enter an amount", "pleaseEnterValidNumber": "Please enter a valid number", "amountGreaterThanZero": "Amount must be greater than zero", "debt": "Debt", "payment": "Payment", "transactions": "Transactions", "inventoryManagement": "Inventory Management", "searchProducts": "Search Products", "addProduct": "Add Product", "newProduct": "New Product", "productName": "Product Name", "purchasePrice": "Purchase Price", "stockQuantity": "Stock Quantity", "imageURLOptional": "Image URL (Optional)", "productAddedSuccessfully": "Product added successfully!", "failedToAddProduct": "Failed to add product: {error}", "pleaseEnterProductName": "Please enter a product name", "pleaseEnterPurchasePrice": "Please enter a purchase price", "stockCannotBeNegative": "Stock cannot be negative", "pleaseEnterStockQuantity": "Please enter a stock quantity", "pleaseEnterValidInteger": "Please enter a valid integer", "noPhone": "No phone", "n_a": "N/A", "transaction": "Transaction", "type": "Type", "total": "Total", "rial": "<PERSON><PERSON>", "date": "Date", "description": "Description", "remaining": "Remaining", "customerNotFound": "Customer not found", "exitAppTitle": "Exit App?", "exitAppMessage": "Do you want to exit the application?", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error"}
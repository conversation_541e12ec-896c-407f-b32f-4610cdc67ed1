import 'package:drift/drift.dart';
import 'package:debt_inventory_app/data/models/customer.dart';

class Transactions extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get customerId => integer().references(Customers, #id)();
  RealColumn get amount => real()();
  TextColumn get type => text().withLength(min: 4, max: 7)();
  TextColumn get notes => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/providers/customer_provider.dart';
import 'package:debt_inventory_app/providers/transaction_provider.dart';
import 'package:debt_inventory_app/utils/context_extensions.dart';

class CustomerDetailScreen extends ConsumerWidget {
  final int customerId;

  const CustomerDetailScreen({super.key, required this.customerId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerAsync = ref.watch(customerProvider);
    final customerLoading = ref.watch(customerLoadingProvider);
    final customerError = ref.watch(customerErrorProvider);

    // Handle loading state
    if (customerLoading) {
      return Scaffold(
        appBar: AppBar(title: Text(context.loc.customerDetails)),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    // Handle error state
    if (customerError != null) {
      return Scaffold(
        appBar: AppBar(title: Text(context.loc.customerDetails)),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Theme.of(context).colorScheme.error),
              const SizedBox(height: 16),
              Text('${context.loc.error}: $customerError'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.pop(),
                child: Text(context.loc.no), // Using "No" as "Back"
              ),
            ],
          ),
        ),
      );
    }

    // Find customer
    Customer? customer;
    try {
      customer = customerAsync.firstWhere((c) => c.id == customerId);
    } catch (e) {
      return Scaffold(
        appBar: AppBar(title: Text(context.loc.customerDetails)),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.person_off, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(context.loc.customerNotFound),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.pop(),
                child: Text(context.loc.no), // Using "No" as "Back"
              ),
            ],
          ),
        ),
      );
    }

    final customerDebt = ref.watch(customerDebtProvider(customerId));

    return PopScope(
      canPop: true, // Allow popping for sub-screens
      onPopInvoked: (didPop) {
        if (didPop) {
          // If the user explicitly popped, no need to do anything
          return;
        }
        // If the system tried to pop (e.g., back button), navigate back using GoRouter
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.customerDetails),
          actions: [
            PopupMenuButton<String>(
              onSelected: (value) async {
                if (value == 'delete') {
                  final shouldDelete = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text('حذف العميل'),
                      content: Text('هل أنت متأكد من حذف العميل "${customer!.name}"؟\nسيتم حذف جميع الحركات المرتبطة به.'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: Text(context.loc.no),
                        ),
                        ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.error,
                          ),
                          child: Text(context.loc.yes),
                        ),
                      ],
                    ),
                  );

                  if (shouldDelete == true) {
                    await ref.read(customerProvider.notifier).deleteCustomer(customerId);
                    if (context.mounted) {
                      context.pop();
                    }
                  }
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
                      const SizedBox(width: 8),
                      Text('حذف العميل'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${context.loc.name}: ${customer.name}',
                          style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: 8),
                      Text(
                          '${context.loc.phone}: ${customer.phone ?? context.loc.n_a}',
                          style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: 8),
                      Text(
                          customerLoading
                              ? context.loc.loading
                              : customerError != null
                                  ? '${context.loc.error}: $customerError'
                                  : context.loc.totalDebtAmount(
                                      "${customerDebt.toStringAsFixed(2)} ${context.loc.rial}"),
                          style: Theme.of(context).textTheme.titleMedium),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Text(context.loc.transactionHistory,
                  style: Theme.of(context).textTheme.titleLarge),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final transactions = ref.watch(customerTransactionsProvider(customerId));
                    return ListView.builder(
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = transactions[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4.0),
                          child: ListTile(
                            title: Text('${context.loc.transaction} ${index + 1}'),
                            subtitle: Text(
                                '${context.loc.type}: ${transaction.type}, ${context.loc.amount}: ${transaction.amount.toStringAsFixed(2)} ${context.loc.rial}'),
                            trailing: const Icon(Icons.arrow_forward_ios),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            // Navigate to Add Transaction for this customer
            context.go('/transactions/new?customerId=$customerId');
          },
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}

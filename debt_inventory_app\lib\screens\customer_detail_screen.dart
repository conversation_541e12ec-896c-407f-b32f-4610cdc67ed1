import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/providers/customer_provider.dart';
import 'package:debt_inventory_app/providers/transaction_provider.dart';
import 'package:debt_inventory_app/utils/context_extensions.dart';

class CustomerDetailScreen extends ConsumerWidget {
  final int customerId;

  const CustomerDetailScreen({super.key, required this.customerId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerAsync = ref.watch(customerProvider);
    final customer = customerAsync.firstWhere((c) => c.id == customerId,
            orElse: () => throw Exception('Customer not found'));
    final customerDebt = ref.watch(customerDebtProvider(customerId));
    final customerLoading = ref.watch(customerLoadingProvider);
    final customerError = ref.watch(customerErrorProvider);

    return PopScope(
      canPop: true, // Allow popping for sub-screens
      onPopInvoked: (didPop) {
        if (didPop) {
          // If the user explicitly popped, no need to do anything
          return;
        }
        // If the system tried to pop (e.g., back button), navigate back using GoRouter
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.customerDetails),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${context.loc.name}: ${customer.name}',
                          style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: 8),
                      Text(
                          '${context.loc.phone}: ${customer.phone ?? context.loc.n_a}',
                          style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: 8),
                      Text(
                          customerLoading
                              ? context.loc.loading
                              : customerError != null
                                  ? '${context.loc.error}: $customerError'
                                  : context.loc.totalDebtAmount(
                                      "${customerDebt.toStringAsFixed(2)} ${context.loc.rial}"),
                          style: Theme.of(context).textTheme.titleMedium),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Text(context.loc.transactionHistory,
                  style: Theme.of(context).textTheme.titleLarge),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final transactions = ref.watch(customerTransactionsProvider(customerId));
                    return ListView.builder(
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = transactions[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4.0),
                          child: ListTile(
                            title: Text('${context.loc.transaction} ${index + 1}'),
                            subtitle: Text(
                                '${context.loc.type}: ${transaction.type}, ${context.loc.amount}: ${transaction.amount.toStringAsFixed(2)} ${context.loc.rial}'),
                            trailing: const Icon(Icons.arrow_forward_ios),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            // Navigate to Add Transaction for this customer
            context.go('/transactions/new?customerId=$customerId');
          },
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}

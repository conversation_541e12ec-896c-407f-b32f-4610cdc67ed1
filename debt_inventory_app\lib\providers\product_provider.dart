import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/data/models/product.dart';
import 'package:debt_inventory_app/main.dart';
import 'package:debt_inventory_app/utils/logger.dart';

final productLoadingProvider = StateProvider<bool>((ref) => false);
final productErrorProvider = StateProvider<String?>((ref) => null);

final productProvider = StateNotifierProvider<ProductNotifier, List<Product>>((ref) {
  final notifier = ProductNotifier(ref.watch(databaseProvider), ref);
  notifier._loadProducts(); // Call load after initialization
  return notifier;
});

class ProductNotifier extends StateNotifier<List<Product>> {
  final AppDatabase _database;
  final Ref _ref;

  ProductNotifier(this._database, this._ref) : super([]);

  void _setLoading(bool loading) {
    _ref.read(productLoadingProvider.notifier).state = loading;
  }

  void _setError(String? error) {
    _ref.read(productErrorProvider.notifier).state = error;
  }

  Future<void> _loadProducts() async {
    _setLoading(true);
    _setError(null);
    try {
      state = await _database.getAllProducts();
    } catch (e) {
      _setError('Failed to load products: $e');
      AppLogger.error('Error loading products', error: e, tag: 'ProductProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addProduct(ProductsCompanion product) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.insertProduct(product);
      await _loadProducts();
    } catch (e) {
      _setError('Failed to add product: $e');
      AppLogger.error('Error adding product', error: e, tag: 'ProductProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateProduct(ProductsCompanion product) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.updateProduct(product);
      await _loadProducts();
    } catch (e) {
      _setError('Failed to update product: $e');
      AppLogger.error('Error updating product', error: e, tag: 'ProductProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteProduct(int id) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.deleteProduct(id);
      await _loadProducts();
    } catch (e) {
      _setError('Failed to delete product: $e');
      AppLogger.error('Error deleting product', error: e, tag: 'ProductProvider');
    } finally {
      _setLoading(false);
    }
  }
}
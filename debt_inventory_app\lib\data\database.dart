import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'models/customer.dart';
import 'models/transaction.dart';
import 'models/product.dart';

part 'database.g.dart';

@DriftDatabase(tables: [Customers, Transactions, Products])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  Future<List<Customer>> getAllCustomers() => select(customers).get();
  Future<Customer> getCustomerById(int id) => (select(customers)..where((tbl) => tbl.id.equals(id))).getSingle();
  Future<int> insertCustomer(CustomersCompanion entry) => into(customers).insert(entry);
  Future<bool> updateCustomer(CustomersCompanion entry) => update(customers).replace(entry);
  Future<int> deleteCustomer(int id) => (delete(customers)..where((tbl) => tbl.id.equals(id))).go();

  Future<List<Transaction>> getTransactionsForCustomer(int customerId) =>
      (select(transactions)..where((tbl) => tbl.customerId.equals(customerId))).get();
  Future<int> insertTransaction(TransactionsCompanion entry) => into(transactions).insert(entry);

  Future<List<Product>> getAllProducts() => select(products).get();
  Future<int> insertProduct(ProductsCompanion entry) => into(products).insert(entry);
  Future<bool> updateProduct(ProductsCompanion entry) => update(products).replace(entry);
  Future<int> deleteProduct(int id) => (delete(products)..where((tbl) => tbl.id.equals(id))).go();
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'db.sqlite'));
    return NativeDatabase.createInBackground(file);
  });
}
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/main.dart';
import 'package:debt_inventory_app/utils/logger.dart';

final customerLoadingProvider = StateProvider<bool>((ref) => false);
final customerErrorProvider = StateProvider<String?>((ref) => null);

final customerProvider = StateNotifierProvider<CustomerNotifier, List<Customer>>((ref) {
  final notifier = CustomerNotifier(ref.watch(databaseProvider), ref);
  notifier._loadCustomers(); // Call load after initialization
  return notifier;
});

class CustomerNotifier extends StateNotifier<List<Customer>> {
  final AppDatabase _database;
  final Ref _ref;

  CustomerNotifier(this._database, this._ref) : super([]);

  void _setLoading(bool loading) {
    _ref.read(customerLoadingProvider.notifier).state = loading;
  }

  void _setError(String? error) {
    _ref.read(customerErrorProvider.notifier).state = error;
  }

  Future<void> _loadCustomers() async {
    _setLoading(true);
    _setError(null);
    try {
      state = await _database.getAllCustomers();
    } catch (e) {
      _setError('Failed to load customers: $e');
      AppLogger.error('Error loading customers', error: e, tag: 'CustomerProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addCustomer(CustomersCompanion customer) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.insertCustomer(customer);
      await _loadCustomers();
    } catch (e) {
      _setError('Failed to add customer: $e');
      AppLogger.error('Error adding customer', error: e, tag: 'CustomerProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateCustomer(CustomersCompanion customer) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.updateCustomer(customer);
      await _loadCustomers();
    } catch (e) {
      _setError('Failed to update customer: $e');
      AppLogger.error('Error updating customer', error: e, tag: 'CustomerProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteCustomer(int id) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.deleteCustomer(id);
      await _loadCustomers();
    } catch (e) {
      _setError('Failed to delete customer: $e');
      AppLogger.error('Error deleting customer', error: e, tag: 'CustomerProvider');
    } finally {
      _setLoading(false);
    }
  }
}
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/main.dart';
import 'package:debt_inventory_app/utils/logger.dart';

final transactionLoadingProvider = StateProvider<bool>((ref) => false);
final transactionErrorProvider = StateProvider<String?>((ref) => null);

final transactionProvider = StateNotifierProvider<TransactionNotifier, List<Transaction>>((ref) {
  final notifier = TransactionNotifier(ref.watch(databaseProvider), ref);
  notifier._loadTransactions(); // Call load after initialization
  return notifier;
});

final customerTransactionsProvider = Provider.family<List<Transaction>, int>((ref, customerId) {
  final transactions = ref.watch(transactionProvider);
  return transactions.where((transaction) => transaction.customerId == customerId).toList();
});

final customerDebtProvider = Provider.family<double, int>((ref, customerId) {
  final transactions = ref.watch(customerTransactionsProvider(customerId));
  double totalDebt = 0.0;

  for (final transaction in transactions) {
    if (transaction.type == 'debt') {
      totalDebt += transaction.amount;
    } else if (transaction.type == 'payment') {
      totalDebt -= transaction.amount;
    }
  }

  return totalDebt;
});

final totalDebtProvider = Provider<double>((ref) {
  final transactions = ref.watch(transactionProvider);
  double totalDebt = 0.0;

  for (final transaction in transactions) {
    if (transaction.type == 'debt') {
      totalDebt += transaction.amount;
    } else if (transaction.type == 'payment') {
      totalDebt -= transaction.amount;
    }
  }

  return totalDebt;
});

class TransactionNotifier extends StateNotifier<List<Transaction>> {
  final AppDatabase _database;
  final Ref _ref;

  TransactionNotifier(this._database, this._ref) : super([]) {
    _loadTransactions();
  }

  void _setLoading(bool loading) {
    _ref.read(transactionLoadingProvider.notifier).state = loading;
  }

  void _setError(String? error) {
    _ref.read(transactionErrorProvider.notifier).state = error;
  }

  Future<void> _loadTransactions() async {
    _setLoading(true);
    _setError(null);
    try {
      state = await _database.getAllTransactions();
    } catch (e) {
      _setError('Failed to load transactions: $e');
      AppLogger.error('Error loading transactions', error: e, tag: 'TransactionProvider');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addTransaction(TransactionsCompanion transaction) async {
    _setLoading(true);
    _setError(null);
    try {
      await _database.insertTransaction(transaction);
      await _loadTransactions();
    } catch (e) {
      _setError('Failed to add transaction: $e');
      AppLogger.error('Error adding transaction', error: e, tag: 'TransactionProvider');
    } finally {
      _setLoading(false);
    }
  }
}

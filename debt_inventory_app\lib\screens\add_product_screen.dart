import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/providers/product_provider.dart';
import 'package:drift/drift.dart' show Value;
import 'package:debt_inventory_app/utils/context_extensions.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen({super.key});

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _stockController = TextEditingController();
  final TextEditingController _imagePathController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _imagePathController.dispose();
    super.dispose();
  }

  void _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _formKey.currentState!.save();

    // Trim and validate inputs
    final name = _nameController.text.trim();
    final priceText = _priceController.text.trim();
    final stockText = _stockController.text.trim();
    final imagePath = _imagePathController.text.trim();

    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.loc.pleaseEnterProductName)),
      );
      return;
    }

    final price = double.tryParse(priceText);
    if (price == null || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.loc.pleaseEnterValidNumber)),
      );
      return;
    }

    final stock = int.tryParse(stockText);
    if (stock == null || stock < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.loc.pleaseEnterValidInteger)),
      );
      return;
    }

    final product = ProductsCompanion(
      name: Value(name),
      purchasePrice: Value(price),
      stock: Value(stock),
      imagePath: Value(imagePath.isEmpty ? null : imagePath),
    );

    await ref.read(productProvider.notifier).addProduct(product);
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(productLoadingProvider);
    final error = ref.watch(productErrorProvider);

    // Listen for errors and show SnackBar
    ref.listen<String?>(productErrorProvider, (previous, next) {
      if (next != null && next.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.failedToAddProduct(next))),
        );
        ref.read(productErrorProvider.notifier).state = null; // Clear the error after showing
      }
    });

    // Listen for successful submission and pop
    ref.listen<bool>(productLoadingProvider, (previous, next) {
      if (previous == true && next == false && error == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.productAddedSuccessfully)),
        );
        context.pop(); // Go back after successful submission
      }
    });

    return PopScope(
      canPop: true, // Allow popping for sub-screens
      onPopInvoked: (didPop) {
        if (didPop) {
          // If the user explicitly popped, no need to do anything
          return;
        }
        // If the system tried to pop (e.g., back button), navigate back using GoRouter
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.addProduct),
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.primary),
            onPressed: () => context.pop(),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(labelText: context.loc.productName),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.loc.pleaseEnterProductName;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _priceController,
                  decoration: InputDecoration(labelText: context.loc.purchasePrice),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.loc.pleaseEnterPurchasePrice;
                    }
                    if (double.tryParse(value) == null) {
                      return context.loc.pleaseEnterValidNumber;
                    }
                    if (double.parse(value) <= 0) {
                      return context.loc.amountGreaterThanZero;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _stockController,
                  decoration: InputDecoration(labelText: context.loc.stockQuantity),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.loc.pleaseEnterStockQuantity;
                    }
                    if (int.tryParse(value) == null) {
                      return context.loc.pleaseEnterValidInteger;
                    }
                    if (int.parse(value) < 0) {
                      return context.loc.stockCannotBeNegative;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _imagePathController,
                  decoration: InputDecoration(labelText: context.loc.imageURLOptional),
                  keyboardType: TextInputType.url,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: isLoading ? null : _submitForm,
                  child: isLoading
                      ? CircularProgressIndicator(color: Theme.of(context).colorScheme.onPrimary)
                      : Text(context.loc.addProduct),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'تطبيق إدارة الديون والمخزون';

  @override
  String get homeDashboardTitle => 'لوحة تحكم الديون والمخزون';

  @override
  String get totalDebt => 'إجمالي الديون';

  @override
  String get customers => 'العملاء';

  @override
  String get products => 'الأصناف';

  @override
  String get newCustomer => 'عميل جديد';

  @override
  String get newTransaction => 'حركة جديدة';

  @override
  String get generateReport => 'توليد كشف حساب';

  @override
  String get debtDistribution => 'توزيع الديون';

  @override
  String get pieChartPlaceholder => 'سيتم عرض الرسم البياني هنا.';

  @override
  String get customersScreenTitle => 'العملاء';

  @override
  String get searchCustomers => 'بحث العملاء';

  @override
  String get addCustomer => 'إضافة عميل';

  @override
  String get customerName => 'اسم العميل';

  @override
  String get phoneNumberOptional => 'رقم الهاتف (اختياري)';

  @override
  String get customerAddedSuccessfully => 'تمت إضافة العميل بنجاح!';

  @override
  String failedToAddCustomer(Object error) {
    return 'فشل في إضافة العميل: $error';
  }

  @override
  String get pleaseEnterCustomerName => 'الرجاء إدخال اسم العميل';

  @override
  String get customerDetails => 'تفاصيل العميل';

  @override
  String get name => 'الاسم';

  @override
  String get phone => 'الهاتف';

  @override
  String totalDebtAmount(Object amount) {
    return 'إجمالي الدين: $amount';
  }

  @override
  String get transactionHistory => 'سجل الحركات';

  @override
  String get addTransaction => 'إضافة حركة جديدة';

  @override
  String get customer => 'العميل';

  @override
  String get transactionType => 'نوع الحركة';

  @override
  String get amount => 'المبلغ';

  @override
  String get notesOptional => 'ملاحظات (اختياري)';

  @override
  String get transactionAddedSuccessfully => 'تمت إضافة الحركة بنجاح!';

  @override
  String failedToAddTransaction(Object error) {
    return 'فشل في إضافة الحركة: $error';
  }

  @override
  String get pleaseSelectCustomer => 'الرجاء اختيار عميل.';

  @override
  String get pleaseSelectTransactionType => 'الرجاء اختيار نوع الحركة.';

  @override
  String get pleaseEnterAmount => 'الرجاء إدخال المبلغ';

  @override
  String get pleaseEnterValidNumber => 'الرجاء إدخال رقم صالح';

  @override
  String get amountGreaterThanZero => 'يجب أن يكون المبلغ أكبر من صفر';

  @override
  String get debt => 'دين';

  @override
  String get payment => 'سداد';

  @override
  String get inventoryManagement => 'إدارة المخزون';

  @override
  String get searchProducts => 'بحث الأصناف';

  @override
  String get addProduct => 'إضافة صنف';

  @override
  String get productName => 'اسم الصنف';

  @override
  String get purchasePrice => 'سعر الشراء';

  @override
  String get stockQuantity => 'الكمية في المخزون';

  @override
  String get imageURLOptional => 'رابط الصورة (اختياري)';

  @override
  String get productAddedSuccessfully => 'تمت إضافة الصنف بنجاح!';

  @override
  String failedToAddProduct(Object error) {
    return 'فشل في إضافة الصنف: $error';
  }

  @override
  String get pleaseEnterProductName => 'الرجاء إدخال اسم الصنف';

  @override
  String get pleaseEnterPurchasePrice => 'الرجاء إدخال سعر الشراء';

  @override
  String get stockCannotBeNegative => 'لا يمكن أن تكون الكمية سالبة';

  @override
  String get pleaseEnterStockQuantity => 'الرجاء إدخال الكمية في المخزون';

  @override
  String get pleaseEnterValidInteger => 'الرجاء إدخال عدد صحيح صالح';

  @override
  String get noPhone => 'لا يوجد هاتف';

  @override
  String get n_a => 'غير متوفر';

  @override
  String get transaction => 'حركة';

  @override
  String get type => 'النوع';

  @override
  String get total => 'الإجمالي';

  @override
  String get rial => 'ريال';

  @override
  String get date => 'التاريخ';

  @override
  String get description => 'الوصف';

  @override
  String get remaining => 'المتبقي';

  @override
  String get customerNotFound => 'العميل غير موجود';
}

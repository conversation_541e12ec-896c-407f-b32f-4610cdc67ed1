import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/providers/customer_provider.dart';
import 'package:debt_inventory_app/providers/product_provider.dart';
import 'package:debt_inventory_app/providers/transaction_provider.dart';
import 'package:debt_inventory_app/utils/context_extensions.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  final List<String> _routes = ['/', '/customers', '/inventory', '/transactions'];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    context.go(_routes[index]);
  }

  Future<bool> _onWillPop() async {
    return await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.loc.exitAppTitle),
        content: Text(context.loc.exitAppMessage),
        actions: [
          TextButton(
            child: Text(context.loc.no),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text(context.loc.yes),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) {
          return;
        }
        final bool shouldPop = await _onWillPop();
        if (shouldPop) {
          // This will close the app
          // For Android, you might use SystemNavigator.pop();
          // For iOS, it's generally not recommended to programmatically exit.
          // For this example, we'll just pop the route if it's the last one.
          // If there's only one route, it will effectively exit.
          if (context.canPop()) {
            context.pop();
          } else {
            // This is the root route, so we might want to exit the app.
            // In a real app, consider platform-specific exit methods.
            // For now, we'll just prevent popping if it's the last route.
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.homeDashboardTitle),
        ),
        drawer: Drawer(
          child: ListView(
            padding: EdgeInsets.zero,
            children: <Widget>[
              DrawerHeader(
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                ),
                child: Text(
                  context.loc.appName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
              ),
              ListTile(
                leading: const Icon(Icons.people),
                title: Text(context.loc.customers),
                onTap: () {
                  context.go('/customers');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.inventory),
                title: Text(context.loc.products),
                onTap: () {
                  context.go('/inventory');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.receipt),
                title: Text(context.loc.transactions),
                onTap: () {
                  context.go('/transactions');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.add_shopping_cart),
                title: Text(context.loc.newProduct),
                onTap: () {
                  context.go('/inventory/new');
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ),
        body: Consumer(
          builder: (context, ref, child) {
            final customers = ref.watch(customerProvider);
            final products = ref.watch(productProvider);
            final totalDebt = ref.watch(totalDebtProvider);
            final customerLoading = ref.watch(customerLoadingProvider);
            final customerError = ref.watch(customerErrorProvider);
            final productLoading = ref.watch(productLoadingProvider);
            final productError = ref.watch(productErrorProvider);
            final transactionLoading = ref.watch(transactionLoadingProvider);
            final transactionError = ref.watch(transactionErrorProvider);

            // Display error messages if any
            if (customerError != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${context.loc.error}: ${customerError}')),
                );
                ref.read(customerErrorProvider.notifier).state = null;
              });
            }
            if (productError != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${context.loc.error}: ${productError}')),
                );
                ref.read(productErrorProvider.notifier).state = null;
              });
            }
            if (transactionError != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${context.loc.error}: ${transactionError}')),
                );
                ref.read(transactionErrorProvider.notifier).state = null;
              });
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Stats Cards
                Row(
                  children: [
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              Text(context.loc.totalDebt, style: Theme.of(context).textTheme.titleMedium),
                              if (transactionLoading)
                                const CircularProgressIndicator()
                              else if (transactionError != null)
                                Text('${context.loc.error}: ${transactionError}')
                              else
                                Text('${totalDebt.toStringAsFixed(2)} ${context.loc.rial}', style: Theme.of(context).textTheme.headlineMedium),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              Text(context.loc.customers, style: Theme.of(context).textTheme.titleMedium),
                              if (customerLoading)
                                const CircularProgressIndicator()
                              else if (customerError != null)
                                Text('${context.loc.error}: ${customerError}')
                              else
                                Text('${customers.length}', style: Theme.of(context).textTheme.headlineMedium),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              Text(context.loc.products, style: Theme.of(context).textTheme.titleMedium),
                              if (productLoading)
                                const CircularProgressIndicator()
                              else if (productError != null)
                                Text('${context.loc.error}: ${productError}')
                              else
                                Text('${products.length}', style: Theme.of(context).textTheme.headlineMedium),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Quick Action Buttons
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () {
                          // Navigate to Add Customer
                          context.go('/customers/new');
                        },
                        icon: const Icon(Icons.person_add),
                        label: Text(context.loc.newCustomer),
                      ),
                      const SizedBox(width: 10), // Add some spacing between buttons
                      ElevatedButton.icon(
                        onPressed: () {
                          // Navigate to Add Transaction
                          context.go('/transactions/new');
                        },
                        icon: const Icon(Icons.add_card),
                        label: Text(context.loc.newTransaction),
                      ),
                      const SizedBox(width: 10), // Add some spacing between buttons
                      ElevatedButton.icon(
                        onPressed: () {
                          // Navigate to Generate Report
                          // context.go('/reports');
                        },
                        icon: const Icon(Icons.picture_as_pdf),
                        label: Text(context.loc.generateReport),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // Pie Chart Placeholder
                Text(context.loc.debtDistribution, style: Theme.of(context).textTheme.titleLarge),
                Expanded(
                  child: Center(
                    child: Text(context.loc.pieChartPlaceholder),
                  ),
                ),
              ],
            );
          },
        ),
        bottomNavigationBar: BottomNavigationBar(
          currentIndex: _selectedIndex,
          selectedItemColor: Theme.of(context).colorScheme.primary,
          unselectedItemColor: Theme.of(context).colorScheme.secondary,
          onTap: _onItemTapped,
          items: [
            BottomNavigationBarItem(icon: const Icon(Icons.home), label: context.loc.home),
            BottomNavigationBarItem(icon: const Icon(Icons.people), label: context.loc.customers),
            BottomNavigationBarItem(icon: const Icon(Icons.inventory), label: context.loc.products),
            BottomNavigationBarItem(icon: const Icon(Icons.receipt), label: context.loc.transactions),
          ],
        ),
      ),
    );
  }
}

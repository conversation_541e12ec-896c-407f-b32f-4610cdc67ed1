import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:debt_inventory_app/main.dart';

void main() {
  group('Debt Inventory App Tests', () {
    testWidgets('App should start and show home screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        ProviderScope(
          child: MyApp(),
        ),
      );

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that we're on the home screen
      expect(find.text('لوحة تحكم الديون والمخزون'), findsOneWidget);
      expect(find.text('إجمالي الديون'), findsOneWidget);
      expect(find.text('العملاء'), findsOneWidget);
      expect(find.text('الأصناف'), findsOneWidget);
    });

    testWidgets('Bottom navigation should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Test navigation to customers
      await tester.tap(find.byIcon(Icons.people));
      await tester.pumpAndSettle();

      // Should navigate to customers screen
      expect(find.text('العملاء'), findsOneWidget);

      // Test navigation to inventory
      await tester.tap(find.byIcon(Icons.inventory));
      await tester.pumpAndSettle();

      // Should navigate to inventory screen
      expect(find.text('إدارة المخزون'), findsOneWidget);

      // Test navigation to transactions
      await tester.tap(find.byIcon(Icons.receipt));
      await tester.pumpAndSettle();

      // Should navigate to transactions screen
      expect(find.text('الحركات'), findsOneWidget);
    });

    testWidgets('Drawer should open and work', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Open drawer
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Verify drawer content
      expect(find.text('تطبيق إدارة الديون والمخزون'), findsOneWidget);
      expect(find.text('العملاء'), findsAtLeastNWidgets(1));
      expect(find.text('الأصناف'), findsAtLeastNWidgets(1));
      expect(find.text('الحركات'), findsAtLeastNWidgets(1));
    });
  });
}

import 'package:drift/drift.dart';
import 'package:debt_inventory_app/providers/transaction_provider.dart';
import 'package:debt_inventory_app/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/providers/customer_provider.dart';
import 'package:debt_inventory_app/utils/context_extensions.dart';

enum TransactionType { debt, payment }

class TransactionFormScreen extends ConsumerStatefulWidget {
  final int? customerId;

  const TransactionFormScreen({super.key, this.customerId});

  @override
  ConsumerState<TransactionFormScreen> createState() => _TransactionFormScreenState();
}

class _TransactionFormScreenState extends ConsumerState<TransactionFormScreen> {
  final _formKey = GlobalKey<FormState>();
  int? _selectedCustomerId;
  TransactionType? _selectedType;
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.customerId != null) {
      _selectedCustomerId = widget.customerId;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      if (_selectedCustomerId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.pleaseSelectCustomer)),
        );
        return;
      }

      if (_selectedType == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.pleaseSelectTransactionType)),
        );
        return;
      }

      final amount = double.parse(_amountController.text);
      if (amount <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.amountGreaterThanZero)),
        );
        return;
      }

      final transaction = TransactionsCompanion(
        customerId: Value(_selectedCustomerId!),
        amount: Value(amount),
        type: Value(_selectedType == TransactionType.debt ? 'debt' : 'payment'),
        notes: Value(_notesController.text.isEmpty ? null : _notesController.text),
        createdAt: Value(DateTime.now()),
      );

      // No need for try-catch here, as the provider handles errors and sets error state
      await ref.read(transactionProvider.notifier).addTransaction(transaction);
    }
  }

  @override
  Widget build(BuildContext context) {
    final customers = ref.watch(customerProvider);
    final isLoading = ref.watch(transactionLoadingProvider);
    final error = ref.watch(transactionErrorProvider);

    // Listen for errors and show SnackBar
    ref.listen<String?>(transactionErrorProvider, (previous, next) {
      if (next != null && next.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.failedToAddTransaction(next))),
        );
        ref.read(transactionErrorProvider.notifier).state = null; // Clear the error after showing
      }
    });

    // Listen for successful submission and pop
    ref.listen<bool>(transactionLoadingProvider, (previous, next) {
      if (previous == true && next == false && error == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.transactionAddedSuccessfully)),
        );
        context.pop(); // Go back after successful submission
      }
    });

    return PopScope(
      canPop: true, // Allow popping for sub-screens
      onPopInvoked: (didPop) {
        if (didPop) {
          // If the user explicitly popped, no need to do anything
          return;
        }
        // If the system tried to pop (e.g., back button), navigate back using GoRouter
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.addTransaction),
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.primary),
            onPressed: () => context.pop(),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                DropdownButtonFormField<int>(
                  value: _selectedCustomerId,
                  decoration: InputDecoration(labelText: context.loc.customer),
                  items: customers.map((customer) {
                    return DropdownMenuItem(
                      value: customer.id,
                      child: Text(customer.name),
                    );
                  }).toList(),
                  onChanged: widget.customerId != null
                      ? null
                      : (value) {
                          setState(() {
                            _selectedCustomerId = value;
                          });
                        },
                  validator: (value) {
                    if (value == null) {
                      return context.loc.pleaseSelectCustomer;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<TransactionType>(
                  value: _selectedType,
                  decoration: InputDecoration(labelText: context.loc.transactionType),
                  items: [
                    DropdownMenuItem(
                      value: TransactionType.debt,
                      child: Text(context.loc.debt),
                    ),
                    DropdownMenuItem(
                      value: TransactionType.payment,
                      child: Text(context.loc.payment),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return context.loc.pleaseSelectTransactionType;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(labelText: context.loc.amount),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.loc.pleaseEnterAmount;
                    }
                    if (double.tryParse(value) == null) {
                      return context.loc.pleaseEnterValidNumber;
                    }
                    if (double.parse(value) <= 0) {
                      return context.loc.amountGreaterThanZero;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  decoration: InputDecoration(labelText: context.loc.notesOptional),
                  maxLines: 3,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: isLoading ? null : _submitForm,
                  child: isLoading
                      ? CircularProgressIndicator(color: Theme.of(context).colorScheme.onPrimary)
                      : Text(context.loc.addTransaction),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/data/database.dart';
import 'package:debt_inventory_app/providers/customer_provider.dart';
import 'package:drift/drift.dart' show Value;
import 'package:flutter/services.dart';
import 'package:debt_inventory_app/utils/context_extensions.dart';

class AddCustomerScreen extends ConsumerStatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  ConsumerState<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends ConsumerState<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _formKey.currentState!.save();

    // Trim whitespace and validate
    final name = _nameController.text.trim();
    final phone = _phoneController.text.trim();

    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.loc.pleaseEnterCustomerName)),
      );
      return;
    }

    final customer = CustomersCompanion(
      name: Value(name),
      phone: Value(phone.isEmpty ? null : phone),
      createdAt: Value(DateTime.now()),
    );

    await ref.read(customerProvider.notifier).addCustomer(customer);
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(customerLoadingProvider);
    final error = ref.watch(customerErrorProvider);

    // Listen for errors and show SnackBar
    ref.listen<String?>(customerErrorProvider, (previous, next) {
      if (next != null && next.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.failedToAddCustomer(next))),
        );
        ref.read(customerErrorProvider.notifier).state = null; // Clear the error after showing
      }
    });

    // Listen for successful submission and pop
    ref.listen<bool>(customerLoadingProvider, (previous, next) {
      if (previous == true && next == false && error == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.loc.customerAddedSuccessfully)),
        );
        context.pop(); // Go back after successful submission
      }
    });

    return PopScope(
      canPop: true, // Allow popping for sub-screens
      onPopInvoked: (didPop) {
        if (didPop) {
          // If the user explicitly popped, no need to do anything
          return;
        }
        // If the system tried to pop (e.g., back button), navigate back using GoRouter
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.addCustomer),
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.primary),
            onPressed: () => context.pop(),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(labelText: context.loc.customerName),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.loc.pleaseEnterCustomerName;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(labelText: context.loc.phoneNumberOptional),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: isLoading ? null : _submitForm,
                  child: isLoading
                      ? CircularProgressIndicator(color: Theme.of(context).colorScheme.onPrimary)
                      : Text(context.loc.addCustomer),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
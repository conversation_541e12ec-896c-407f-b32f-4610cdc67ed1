import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:debt_inventory_app/l10n/app_localizations.dart';
import 'package:debt_inventory_app/utils/context_extensions.dart';
import 'package:debt_inventory_app/providers/transaction_provider.dart';

class TransactionsScreen extends ConsumerWidget {
  const TransactionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopScope(
      canPop: true, // Allow popping for sub-screens
      onPopInvoked: (didPop) {
        if (didPop) {
          // If the user explicitly popped, no need to do anything
          return;
        }
        // If the system tried to pop (e.g., back button), navigate back using GoRouter
        context.pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.loc.transactions),
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.primary),
            onPressed: () => context.pop(),
          ),
        ),
        body: Consumer(
          builder: (BuildContext context, WidgetRef ref, Widget? child) {
            final transactions = ref.watch(transactionProvider);
            return ListView.builder(
              itemCount: transactions.length,
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                  child: ListTile(
                    title: Text('${context.loc.transaction} ${index + 1}'),
                    subtitle: Text(
                      '${context.loc.type}: ${transaction.type}, ${context.loc.amount}: ${transaction.amount.toStringAsFixed(2)} ${context.loc.rial}',
                    ),
                  ),
                );
              },
            );
          },
        )
      ),
    );
  }
}
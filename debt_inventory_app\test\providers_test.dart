import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:debt_inventory_app/providers/customer_provider.dart';
import 'package:debt_inventory_app/providers/product_provider.dart';
import 'package:debt_inventory_app/providers/transaction_provider.dart';
import 'package:debt_inventory_app/providers/locale_provider.dart';
import 'package:flutter/material.dart';

void main() {
  group('Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('Customer provider should initialize with empty list', () {
      final customers = container.read(customerProvider);
      expect(customers, isEmpty);
    });

    test('Customer loading provider should initialize as false', () {
      final isLoading = container.read(customerLoadingProvider);
      expect(isLoading, false);
    });

    test('Customer error provider should initialize as null', () {
      final error = container.read(customerErrorProvider);
      expect(error, null);
    });

    test('Product provider should initialize with empty list', () {
      final products = container.read(productProvider);
      expect(products, isEmpty);
    });

    test('Product loading provider should initialize as false', () {
      final isLoading = container.read(productLoadingProvider);
      expect(isLoading, false);
    });

    test('Product error provider should initialize as null', () {
      final error = container.read(productErrorProvider);
      expect(error, null);
    });

    test('Transaction provider should initialize with empty list', () {
      final transactions = container.read(transactionProvider);
      expect(transactions, isEmpty);
    });

    test('Transaction loading provider should initialize as false', () {
      final isLoading = container.read(transactionLoadingProvider);
      expect(isLoading, false);
    });

    test('Transaction error provider should initialize as null', () {
      final error = container.read(transactionErrorProvider);
      expect(error, null);
    });

    test('Locale provider should initialize with Arabic locale', () {
      final locale = container.read(localeProvider);
      expect(locale, const Locale('ar'));
    });

    test('Total debt provider should calculate correctly with empty transactions', () {
      final totalDebt = container.read(totalDebtProvider);
      expect(totalDebt, 0.0);
    });

    test('Customer debt provider should calculate correctly with no transactions', () {
      final customerDebt = container.read(customerDebtProvider(1));
      expect(customerDebt, 0.0);
    });

    test('Customer transactions provider should return empty list for non-existent customer', () {
      final customerTransactions = container.read(customerTransactionsProvider(999));
      expect(customerTransactions, isEmpty);
    });

    test('Locale provider should update correctly', () {
      // Initial value
      expect(container.read(localeProvider), const Locale('ar'));
      
      // Update to English
      container.read(localeProvider.notifier).state = const Locale('en');
      expect(container.read(localeProvider), const Locale('en'));
      
      // Update back to Arabic
      container.read(localeProvider.notifier).state = const Locale('ar');
      expect(container.read(localeProvider), const Locale('ar'));
    });

    test('Loading states should update correctly', () {
      // Customer loading
      container.read(customerLoadingProvider.notifier).state = true;
      expect(container.read(customerLoadingProvider), true);
      
      container.read(customerLoadingProvider.notifier).state = false;
      expect(container.read(customerLoadingProvider), false);
      
      // Product loading
      container.read(productLoadingProvider.notifier).state = true;
      expect(container.read(productLoadingProvider), true);
      
      // Transaction loading
      container.read(transactionLoadingProvider.notifier).state = true;
      expect(container.read(transactionLoadingProvider), true);
    });

    test('Error states should update correctly', () {
      const errorMessage = 'Test error message';
      
      // Customer error
      container.read(customerErrorProvider.notifier).state = errorMessage;
      expect(container.read(customerErrorProvider), errorMessage);
      
      container.read(customerErrorProvider.notifier).state = null;
      expect(container.read(customerErrorProvider), null);
      
      // Product error
      container.read(productErrorProvider.notifier).state = errorMessage;
      expect(container.read(productErrorProvider), errorMessage);
      
      // Transaction error
      container.read(transactionErrorProvider.notifier).state = errorMessage;
      expect(container.read(transactionErrorProvider), errorMessage);
    });
  });
}
